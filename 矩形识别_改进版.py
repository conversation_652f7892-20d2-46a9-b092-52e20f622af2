import time, os, sys
import math
import cv_lite  # 导入cv_lite扩展模块
import ulab.numpy as np  # 导入numpy库
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import UART, FPIOA

# --------------------------- 硬件初始化 ---------------------------
# 串口初始化
fpioa = FPIOA()
fpioa.set_function(5, FPIOA.UART2_TXD)
fpioa.set_function(6, FPIOA.UART2_RXD)
uart2 = UART(UART.UART2, 115200)

# 屏幕分辨率设置
lcd_width = 800
lcd_height = 480

# 摄像头初始化
sensor = Sensor(width=1280, height=960)
sensor.reset()
sensor.set_framesize(width=320, height=240)  # 降低分辨率提高帧率
sensor.set_pixformat(Sensor.RGB565)  # 保留彩色用于紫色色块检测

# 视野中心坐标（用于偏差计算）
FRAME_CENTER_X = 320 // 2  # 160
FRAME_CENTER_Y = 240 // 2  # 120

# 显示初始化
Display.init(Display.ST7701, width=lcd_width, height=lcd_height, to_ide=True)
MediaManager.init()
sensor.run()

# --------------------------- 配置参数 ---------------------------
# 矩形检测核心参数（基于cv_lite）
canny_thresh1      = 50        # Canny边缘检测低阈值
canny_thresh2      = 150       # Canny边缘检测高阈值
approx_epsilon     = 0.04      # 多边形拟合精度（越小越精确）
area_min_ratio     = 0.005     # 最小面积比例（相对于图像总面积）
max_angle_cos      = 0.3       # 角度余弦阈值（越小越接近矩形）
gaussian_blur_size = 3         # 高斯模糊核尺寸（奇数）

# 原有筛选参数
MIN_AREA = 100               # 最小面积阈值
MAX_AREA = 100000             # 最大面积阈值
MIN_ASPECT_RATIO = 0.3        # 最小宽高比
MAX_ASPECT_RATIO = 3.0        # 最大宽高比

# 虚拟坐标与圆形参数
BASE_RADIUS = 45              # 基础半径（虚拟坐标单位）
POINTS_PER_CIRCLE = 24        # 圆形采样点数量
PURPLE_THRESHOLD = (20, 60, 15, 70, -70, -20)  # 紫色色块阈值

# 基础矩形参数（固定方向，不再自动切换）
RECT_WIDTH = 210    # 固定矩形宽度
RECT_HEIGHT = 95    # 固定矩形高度

# --------------------------- 工具函数 ---------------------------
def calculate_distance(p1, p2):
    return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])** 2)

def calculate_center(points):
    if not points:
        return (0, 0)
    sum_x = sum(p[0] for p in points)
    sum_y = sum(p[1] for p in points)
    return (sum_x / len(points), sum_y / len(points))

def is_valid_rect(corners):
    edges = [calculate_distance(corners[i], corners[(i+1)%4]) for i in range(4)]

    # 对边比例校验
    ratio1 = edges[0] / max(edges[2], 0.1)
    ratio2 = edges[1] / max(edges[3], 0.1)
    valid_ratio = 0.5 < ratio1 < 1.5 and 0.5 < ratio2 < 1.5

    # 面积校验
    area = 0
    for i in range(4):
        x1, y1 = corners[i]
        x2, y2 = corners[(i+1) % 4]
        area += (x1 * y2 - x2 * y1)
    area = abs(area) / 2
    valid_area = MIN_AREA < area < MAX_AREA

    # 宽高比校验
    width = max(p[0] for p in corners) - min(p[0] for p in corners)
    height = max(p[1] for p in corners) - min(p[1] for p in corners)
    aspect_ratio = width / max(height, 0.1)
    valid_aspect = MIN_ASPECT_RATIO < aspect_ratio < MAX_ASPECT_RATIO

    return valid_ratio and valid_area and valid_aspect

def detect_purple_blobs(img):
    return img.find_blobs(
        [PURPLE_THRESHOLD],
        pixels_threshold=100,
        area_threshold=100,
        merge=True
    )

def get_perspective_matrix(src_pts, dst_pts):
    """计算透视变换矩阵"""
    A = []
    B = []
    for i in range(4):
        x, y = src_pts[i]
        u, v = dst_pts[i]
        A.append([x, y, 1, 0, 0, 0, -u*x, -u*y])
        A.append([0, 0, 0, x, y, 1, -v*x, -v*y])
        B.append(u)
        B.append(v)

    # 高斯消元求解矩阵
    n = 8
    for i in range(n):
        max_row = i
        for j in range(i, len(A)):
            if abs(A[j][i]) > abs(A[max_row][i]):
                max_row = j
        A[i], A[max_row] = A[max_row], A[i]
        B[i], B[max_row] = B[max_row], B[i]

        pivot = A[i][i]
        if abs(pivot) < 1e-8:
            return None
        for j in range(i, n):
            A[i][j] /= pivot
        B[i] /= pivot

        for j in range(len(A)):
            if j != i and A[j][i] != 0:
                factor = A[j][i]
                for k in range(i, n):
                    A[j][k] -= factor * A[i][k]
                B[j] -= factor * B[i]

    return [
        [B[0], B[1], B[2]],
        [B[3], B[4], B[5]],
        [B[6], B[7], 1.0]
    ]

def transform_points(points, matrix):
    """应用透视变换将虚拟坐标映射到原始图像坐标"""
    transformed = []
    for (x, y) in points:
        x_hom = x * matrix[0][0] + y * matrix[0][1] + matrix[0][2]
        y_hom = x * matrix[1][0] + y * matrix[1][1] + matrix[1][2]
        w_hom = x * matrix[2][0] + y * matrix[2][1] + matrix[2][2]
        if abs(w_hom) > 1e-8:
            transformed.append((x_hom / w_hom, y_hom / w_hom))
    return transformed

def sort_corners(corners):
    """将矩形角点按左上、右上、右下、左下顺序排序"""
    center = calculate_center(corners)
    sorted_corners = sorted(corners, key=lambda p: math.atan2(p[1]-center[1], p[0]-center[0]))

    # 调整顺序为左上、右上、右下、左下
    if len(sorted_corners) == 4:
        left_top = min(sorted_corners, key=lambda p: p[0]+p[1])
        index = sorted_corners.index(left_top)
        sorted_corners = sorted_corners[index:] + sorted_corners[:index]
    return sorted_corners

def get_rectangle_orientation(corners):
    """计算矩形的主方向角（水平边与x轴的夹角）"""
    if len(corners) != 4:
        return 0

    # 计算上边和右边的向量
    top_edge = (corners[1][0] - corners[0][0], corners[1][1] - corners[0][1])
    right_edge = (corners[2][0] - corners[1][0], corners[2][1] - corners[1][1])

    # 选择较长的边作为主方向
    if calculate_distance(corners[0], corners[1]) > calculate_distance(corners[1], corners[2]):
        main_edge = top_edge
    else:
        main_edge = right_edge

    # 计算主方向角（弧度）
    angle = math.atan2(main_edge[1], main_edge[0])
    return angle

# --------------------------- 改进的控制类 ---------------------------
# 坐标平滑滤波器
class CoordinateFilter:
    def __init__(self, window_size=5):
        self.window_size = window_size
        self.x_history = []
        self.y_history = []

    def update(self, x, y):
        """更新坐标历史并返回滤波后的坐标"""
        self.x_history.append(x)
        self.y_history.append(y)

        # 保持窗口大小
        if len(self.x_history) > self.window_size:
            self.x_history.pop(0)
        if len(self.y_history) > self.window_size:
            self.y_history.pop(0)

        # 计算移动平均
        filtered_x = sum(self.x_history) / len(self.x_history)
        filtered_y = sum(self.y_history) / len(self.y_history)

        return filtered_x, filtered_y

    def is_stable(self, threshold=5):
        """检查坐标是否稳定（变化幅度小于阈值）"""
        if len(self.x_history) < self.window_size:
            return False

        x_var = max(self.x_history) - min(self.x_history)
        y_var = max(self.y_history) - min(self.y_history)

        return x_var < threshold and y_var < threshold

# 改进的PID控制器
class EnhancedPID:
    def __init__(self, kp=0.5, ki=0.1, kd=0.2, dead_zone=2, max_output=50):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.dead_zone = dead_zone  # 死区范围
        self.max_output = max_output
        self.prev_error = 0
        self.integral = 0
        self.prev_time = ticks_ms()

    def compute(self, error):
        # 死区控制：小偏差时不输出
        if abs(error) < self.dead_zone:
            return 0

        current_time = ticks_ms()
        dt = (current_time - self.prev_time) / 1000.0
        if dt <= 0: dt = 0.01

        # 积分项计算和限幅
        self.integral += error * dt
        if self.integral > self.max_output: self.integral = self.max_output
        elif self.integral < -self.max_output: self.integral = -self.max_output

        # 微分项计算
        derivative = (error - self.prev_error) / dt

        # PID输出计算
        output = self.kp * error + self.ki * self.integral + self.kd * derivative

        # 输出限幅
        if output > self.max_output: output = self.max_output
        elif output < -self.max_output: output = -self.max_output

        self.prev_error = error
        self.prev_time = current_time
        return output

    def reset(self):
        """重置PID状态"""
        self.prev_error = 0
        self.integral = 0
        self.prev_time = ticks_ms()

# 跟踪状态管理器
class TrackingStateManager:
    def __init__(self, stable_frames_required=5, lost_frames_threshold=10):
        self.stable_frames_required = stable_frames_required
        self.lost_frames_threshold = lost_frames_threshold
        self.stable_count = 0
        self.lost_count = 0
        self.is_tracking = False
        self.last_valid_center = None

    def update(self, rect_detected, center_coords=None, is_stable=False):
        """更新跟踪状态"""
        if rect_detected and is_stable:
            self.stable_count += 1
            self.lost_count = 0
            self.last_valid_center = center_coords

            # 连续稳定检测足够帧数后开始跟踪
            if self.stable_count >= self.stable_frames_required:
                self.is_tracking = True
        else:
            self.stable_count = 0
            self.lost_count += 1

            # 连续丢失超过阈值后停止跟踪
            if self.lost_count >= self.lost_frames_threshold:
                self.is_tracking = False
                self.last_valid_center = None

        return self.is_tracking

    def get_status_text(self):
        """获取状态文本用于显示"""
        if self.is_tracking:
            return "TRACKING"
        elif self.stable_count > 0:
            return f"STABILIZING {self.stable_count}/{self.stable_frames_required}"
        else:
            return f"SEARCHING (lost:{self.lost_count})"

# 发送指令函数
def send_gimbal_command(pan_cmd, tilt_cmd, rect_x=0, rect_y=0):
    try:
        pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
        tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
        rect_x = int(rect_x) if rect_x is not None else 0
        rect_y = int(rect_y) if rect_y is not None else 0

        pan_int = int(pan_cmd * 10) + 1000
        tilt_int = int(tilt_cmd * 10) + 1000

        rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
        rx_order[1] = (pan_int >> 8) & 0xFF
        rx_order[2] = pan_int & 0xFF
        rx_order[3] = (tilt_int >> 8) & 0xFF
        rx_order[4] = tilt_int & 0xFF
        rx_order[5] = (rect_x >> 8) & 0xFF
        rx_order[6] = rect_x & 0xFF
        rx_order[7] = (rect_y >> 8) & 0xFF
        rx_order[8] = rect_y & 0xFF

        uart2.write(bytes(rx_order))
        print(f"云台指令: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}")
    except Exception as e:
        print(f"发送指令失败: {e}")

# 初始化控制组件
coord_filter = CoordinateFilter(window_size=3)  # 3帧平滑
pid_x = EnhancedPID(kp=0.2, ki=0.05, kd=0.08, dead_zone=3, max_output=30)  # 优化参数
pid_y = EnhancedPID(kp=0.2, ki=0.05, kd=0.08, dead_zone=3, max_output=30)
tracking_manager = TrackingStateManager(stable_frames_required=3, lost_frames_threshold=8)

# --------------------------- 主循环 ---------------------------
clock = time.clock()
image_shape = [sensor.height(), sensor.width()]  # [高, 宽] 用于cv_lite

print("矩形跟踪系统启动 - 改进版")
print("特性: 坐标滤波 + 稳定性检测 + 优化PID控制")

while True:
    clock.tick()
    img = sensor.snapshot()

    # 1. 检测紫色色块（保留原有功能）
    purple_blobs = detect_purple_blobs(img)
    for blob in purple_blobs:
        img.draw_rectangle(blob[0:4], color=(255, 0, 255), thickness=1)
        img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 255), thickness=1)

    # 2. 矩形检测（使用cv_lite）
    gray_img = img.to_grayscale()
    img_np = gray_img.to_numpy_ref()

    rects = cv_lite.grayscale_find_rectangles_with_corners(
        image_shape,       # 图像尺寸 [高, 宽]
        img_np,            # 灰度图数据
        canny_thresh1,     # Canny低阈值
        canny_thresh2,     # Canny高阈值
        approx_epsilon,    # 多边形拟合精度
        area_min_ratio,    # 最小面积比例
        max_angle_cos,     # 角度余弦阈值
        gaussian_blur_size # 高斯模糊尺寸
    )

    # 3. 筛选最小矩形
    min_area = float('inf')
    smallest_rect = None
    smallest_rect_corners = None

    for rect in rects:
        x, y, w, h = rect[0], rect[1], rect[2], rect[3]
        corners = [
            (rect[4], rect[5]),   # 角点1
            (rect[6], rect[7]),   # 角点2
            (rect[8], rect[9]),   # 角点3
            (rect[10], rect[11])  # 角点4
        ]

        if is_valid_rect(corners):
            area = w * h
            if area < min_area:
                min_area = area
                smallest_rect = (x, y, w, h)
                smallest_rect_corners = corners

    # 4. 处理检测到的矩形
    if smallest_rect and smallest_rect_corners:
        corners = smallest_rect_corners
        sorted_corners = sort_corners(corners)

        # 绘制矩形边框和角点
        for i in range(4):
            x1, y1 = sorted_corners[i]
            x2, y2 = sorted_corners[(i+1) % 4]
            img.draw_line(x1, y1, x2, y2, color=(255, 0, 0), thickness=2)
        for p in sorted_corners:
            img.draw_circle(p[0], p[1], 5, color=(0, 255, 0), thickness=2)

        # 计算矩形中心
        rect_center = calculate_center(sorted_corners)
        rect_center_int = (int(round(rect_center[0])), int(round(rect_center[1])))
        img.draw_circle(rect_center_int[0], rect_center_int[1], 4, color=(0, 255, 255), thickness=2)

        # 固定虚拟矩形
        virtual_rect = [
            (0, 0),
            (RECT_WIDTH, 0),
            (RECT_WIDTH, RECT_HEIGHT),
            (0, RECT_HEIGHT)
        ]

        radius_x = BASE_RADIUS
        radius_y = BASE_RADIUS
        virtual_center = (RECT_WIDTH / 2, RECT_HEIGHT / 2)

        # 生成虚拟圆形点集
        virtual_circle_points = []
        for i in range(POINTS_PER_CIRCLE):
            angle_rad = 2 * math.pi * i / POINTS_PER_CIRCLE
            x_virt = virtual_center[0] + radius_x * math.cos(angle_rad)
            y_virt = virtual_center[1] + radius_y * math.sin(angle_rad)
            virtual_circle_points.append((x_virt, y_virt))

        # 透视变换和坐标映射
        matrix = get_perspective_matrix(virtual_rect, sorted_corners)
        if matrix:
            mapped_points = transform_points(virtual_circle_points, matrix)
            int_points = [(int(round(x)), int(round(y))) for x, y in mapped_points]

            # 绘制圆形
            for (px, py) in int_points:
                img.draw_circle(px, py, 2, color=(255, 0, 255), thickness=2)

            # 处理圆心坐标
            mapped_center = transform_points([virtual_center], matrix)
            if mapped_center:
                raw_cx, raw_cy = mapped_center[0]

                # 应用坐标滤波
                filtered_cx, filtered_cy = coord_filter.update(raw_cx, raw_cy)
                cx, cy = int(round(filtered_cx)), int(round(filtered_cy))

                # 绘制原始中心点（红色）和滤波后中心点（蓝色）
                img.draw_circle(int(round(raw_cx)), int(round(raw_cy)), 2, color=(255, 0, 0), thickness=1)
                img.draw_circle(cx, cy, 3, color=(0, 0, 255), thickness=2)

                # 检查坐标稳定性
                is_stable = coord_filter.is_stable(threshold=8)

                # 更新跟踪状态
                is_tracking = tracking_manager.update(True, (cx, cy), is_stable)

                # 跟踪控制逻辑
                if is_tracking:
                    # 计算偏差
                    error_x = cx - FRAME_CENTER_X
                    error_y = cy - FRAME_CENTER_Y

                    # PID控制
                    pan_cmd = pid_x.compute(error_x)
                    tilt_cmd = pid_y.compute(error_y)

                    # 发送控制指令
                    send_gimbal_command(pan_cmd, tilt_cmd, cx, cy)

                    # 显示跟踪信息
                    img.draw_string_advanced(10, 50, 16, f"Error: X={error_x:.1f}, Y={error_y:.1f}", color=(0, 255, 0))
                    img.draw_string_advanced(10, 70, 16, f"PID: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}", color=(0, 255, 0))
                else:
                    # 不在跟踪状态时发送停止指令
                    send_gimbal_command(0, 0, cx, cy)

                # 显示跟踪状态
                status_text = tracking_manager.get_status_text()
                status_color = (0, 255, 0) if is_tracking else (255, 255, 0) if is_stable else (255, 0, 0)
                img.draw_string_advanced(10, 30, 16, f"Status: {status_text}", color=status_color)

    else:
        # 没有检测到矩形时更新跟踪状态
        tracking_manager.update(False)
        status_text = tracking_manager.get_status_text()
        img.draw_string_advanced(10, 30, 16, f"Status: {status_text}", color=(255, 0, 0))

    # 5. 显示与性能统计
    fps = clock.fps()
    img.draw_string_advanced(10, 10, 20, f"FPS: {fps:.1f}", color=(255, 255, 255))

    # 显示图像
    Display.show_image(img,
                      x=round((lcd_width-sensor.width())/2),
                      y=round((lcd_height-sensor.height())/2))

    print(f"FPS: {fps:.1f}")  # 打印FPS
